#property copyright "Trading Sandbox"
#property version   "1.02"
#property indicator_chart_window
#property indicator_buffers 0
#property indicator_plots 0

input string ____Chart_Positioning____ = "═════ Chart Positioning ═════";
input double VerticalShiftPips = 25.0;
input int HorizontalShiftBars = 0;
input int VerticalScale = 0;

input string ____Chart_Visibility____ = "═════ Chart Visibility ═════";
input bool ShowOriginalChart = false;
input bool ShowShiftedChart = true;

input string ____Shifted_Chart_Colors____ = "═════ Shifted Chart Colors ═════";
input color ShiftedUpBarColor = clrGreen;
input color ShiftedDownBarColor = clrFireBrick;

double pip_multiplier;
double shift_value;
string prefix = "DC_";

int OnInit()
{
    // Remove any existing objects
    ObjectsDeleteAll(0, prefix);
    
    // Pip multiplier setup
    string symbol = Symbol();
    if (StringFind(symbol, "JPY") != -1)
        pip_multiplier = 0.01;
    else if (StringFind(symbol, "XAU") != -1 || StringFind(symbol, "GOLD") != -1)
        pip_multiplier = 0.1;
    else
        pip_multiplier = 0.0001;

    shift_value = VerticalShiftPips * pip_multiplier;

    IndicatorSetString(INDICATOR_SHORTNAME, 
        "Double Chart (V: " + DoubleToString(VerticalShiftPips, 1) + 
        " pips, H: " + IntegerToString(HorizontalShiftBars) + 
        " bars, S: " + IntegerToString(VerticalScale) + ")");
    IndicatorSetInteger(INDICATOR_DIGITS, Digits());

    return INIT_SUCCEEDED;
}

void DrawCandle(int bar_index, datetime bar_time, double o, double h, double l, double c, color bar_color)
{
    string body_name = prefix + "body_" + IntegerToString(bar_index);
    string wick_up_name = prefix + "wick_up_" + IntegerToString(bar_index);
    string wick_down_name = prefix + "wick_down_" + IntegerToString(bar_index);

    // Delete existing objects for this bar
    ObjectDelete(0, body_name);
    ObjectDelete(0, wick_up_name);
    ObjectDelete(0, wick_down_name);

    // Calculate body dimensions
    double body_top = MathMax(o, c);
    double body_bottom = MathMin(o, c);

    // Calculate time coordinates
    int period_seconds = PeriodSeconds();
    datetime wick_time = bar_time + period_seconds / 2;  // Center of the bar for wicks

    // Create narrower body (70% width) centered on the bar
    int body_width = (int)(period_seconds * 0.7);
    int body_offset = (period_seconds - body_width) / 2;
    datetime body_left = bar_time + body_offset;
    datetime body_right = body_left + body_width;

    // Draw candle body (rectangle) - ensure proper coordinate order
    // MT5 Rectangle: time1, price1, time2, price2 where time1 < time2
    if (ObjectCreate(0, body_name, OBJ_RECTANGLE, 0, body_left, body_bottom, body_right, body_top))
    {
        ObjectSetInteger(0, body_name, OBJPROP_COLOR, bar_color);
        ObjectSetInteger(0, body_name, OBJPROP_FILL, true);
        ObjectSetInteger(0, body_name, OBJPROP_BACK, false);
        ObjectSetInteger(0, body_name, OBJPROP_SELECTABLE, false);
        ObjectSetInteger(0, body_name, OBJPROP_SELECTED, false);
        ObjectSetInteger(0, body_name, OBJPROP_WIDTH, 1);
    }

    // Draw upper wick (trend line) - centered on the bar
    if (h > body_top)
    {
        ObjectCreate(0, wick_up_name, OBJ_TREND, 0, wick_time, body_top, wick_time, h);
        ObjectSetInteger(0, wick_up_name, OBJPROP_COLOR, bar_color);
        ObjectSetInteger(0, wick_up_name, OBJPROP_STYLE, STYLE_SOLID);
        ObjectSetInteger(0, wick_up_name, OBJPROP_WIDTH, 1);
        ObjectSetInteger(0, wick_up_name, OBJPROP_BACK, false);
        ObjectSetInteger(0, wick_up_name, OBJPROP_SELECTABLE, false);
        ObjectSetInteger(0, wick_up_name, OBJPROP_SELECTED, false);
        ObjectSetInteger(0, wick_up_name, OBJPROP_RAY, false);
    }

    // Draw lower wick (trend line) - centered on the bar
    if (l < body_bottom)
    {
        ObjectCreate(0, wick_down_name, OBJ_TREND, 0, wick_time, l, wick_time, body_bottom);
        ObjectSetInteger(0, wick_down_name, OBJPROP_COLOR, bar_color);
        ObjectSetInteger(0, wick_down_name, OBJPROP_STYLE, STYLE_SOLID);
        ObjectSetInteger(0, wick_down_name, OBJPROP_WIDTH, 1);
        ObjectSetInteger(0, wick_down_name, OBJPROP_BACK, false);
        ObjectSetInteger(0, wick_down_name, OBJPROP_SELECTABLE, false);
        ObjectSetInteger(0, wick_down_name, OBJPROP_SELECTED, false);
        ObjectSetInteger(0, wick_down_name, OBJPROP_RAY, false);
    }
}

int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime& time[],
                const double& open[],
                const double& high[],
                const double& low[],
                const double& close[],
                const long& tick_volume[],
                const long& volume[],
                const int& spread[])
{
    if (rates_total < 2) return 0;

    // Clear old objects if recalculating from start
    if (prev_calculated == 0)
        ObjectsDeleteAll(0, prefix);

    int start = prev_calculated > 0 ? prev_calculated - 1 : 0;
    
    // Limit drawing to recent bars to improve performance
    int chart_start = MathMax(0, rates_total - 200);  // Last 200 bars
    int chart_end = rates_total - 1;

    for (int i = chart_start; i <= chart_end; i++)
    {
        if (ShowShiftedChart)
        {
            bool draw_bar = true;
            int source_index = i - HorizontalShiftBars;

            if (VerticalScale > 0)
            {
                int bar_position = rates_total - 1 - i;
                if ((bar_position % (VerticalScale + 1)) != 0)
                {
                    draw_bar = false;
                }
                else
                {
                    int bar_sequence = bar_position / (VerticalScale + 1);
                    source_index = (rates_total - 1 - bar_sequence) - HorizontalShiftBars;
                }
            }

            if (draw_bar && source_index >= 0 && source_index < rates_total)
            {
                double shifted_open = open[source_index] + shift_value;
                double shifted_high = high[source_index] + shift_value;
                double shifted_low = low[source_index] + shift_value;
                double shifted_close = close[source_index] + shift_value;
                
                // Only draw bars that are clearly up or down
                if (close[source_index] > open[source_index])
                {
                    // Up bar - draw full green
                    DrawCandle(i, time[i], shifted_open, shifted_high, shifted_low, shifted_close, ShiftedUpBarColor);
                }
                else if (close[source_index] < open[source_index])
                {
                    // Down bar - draw full red
                    DrawCandle(i, time[i], shifted_open, shifted_high, shifted_low, shifted_close, ShiftedDownBarColor);
                }
                // Doji bars (open == close) - don't draw anything
            }
        }
    }

    return rates_total;
}

void OnDeinit(const int reason)
{
    ObjectsDeleteAll(0, prefix);
    Comment("");
}
