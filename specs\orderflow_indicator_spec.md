# OrderFlow Indicator Specification

Based on the properties configuration shown in specs/props.png

## Input Parameters

### Basic Configuration
- **Tick size in pips**: `20.0`
- **Number of bars to process**: `50`

### Visual Settings
- **Color for buy-side rectangles**: `Blue`
- **Color for sell-side rectangles**: `Red`

### Volume Analysis
- **Volume adjustment multiplier**: `1.0`

### Display Options
- **BigNumbersFormatting**: `true`
- **ShowDelta**: `true`
- **ShowData**: `true`
- **ShowImbalances**: `true`

### Imbalance Detection
- **ImbalancesMultiplier**: `3`

### Chart Appearance
- **ChartBackgroundColor**: `White`
- **Spacing**: `2`
- **RemoveGrid**: `true`
- **ChartTextGridColor**: `Black`

## Implementation Notes

### Key Features Required:
1. **Volume Profile Display**: Show horizontal bars representing buy/sell volume at each price level
2. **Imbalance Detection**: Highlight levels where buy/sell ratio exceeds the ImbalancesMultiplier (3:1 ratio)
3. **Delta Calculation**: Display cumulative delta for each bar
4. **Color Coding**: Blue for buy volume, Red for sell volume
5. **Grid Management**: Remove chart grid when RemoveGrid is true
6. **Number Formatting**: Use big numbers formatting when enabled

### Visual Layout:
- Process maximum 50 bars
- Show individual price levels with volume bars
- Display volume numbers in buy|sell format
- Highlight imbalances with special formatting
- Use specified colors for different volume types

### Chart Integration:
- White background when specified
- Black text/grid colors
- Configurable spacing between elements
- Option to remove default chart grid for cleaner appearance

## Parameters for MT5 Implementation

```mql5
input double TickSizeInPips = 20.0;
input int NumberOfBarsToProcess = 50;
input color BuyVolumeColor = clrBlue;
input color SellVolumeColor = clrRed;
input double VolumeAdjustmentMultiplier = 1.0;
input bool BigNumbersFormatting = true;
input bool ShowDelta = true;
input bool ShowData = true;
input bool ShowImbalances = true;
input int ImbalancesMultiplier = 3;
input color ChartBackgroundColor = clrWhite;
input int Spacing = 2;
input bool RemoveGrid = true;
input color ChartTextGridColor = clrBlack;
```