#property copyright "Trading Sandbox"
#property version   "1.00"
#property indicator_separate_window
#property indicator_buffers 8
#property indicator_plots 4

#property indicator_label1 "Buy Volume"
#property indicator_type1 DRAW_HISTOGRAM
#property indicator_color1 clrLimeGreen
#property indicator_width1 3

#property indicator_label2 "Sell Volume"
#property indicator_type2 DRAW_HISTOGRAM
#property indicator_color2 clrRed
#property indicator_width2 3

#property indicator_label3 "Imbalance"
#property indicator_type3 DRAW_HISTOGRAM
#property indicator_color3 clrYellow
#property indicator_width3 5

#property indicator_label4 "Delta"
#property indicator_type4 DRAW_LINE
#property indicator_color4 clrWhite
#property indicator_width4 2

input int BarsToAnalyze = 100;
input int PriceLevelsPerBar = 10;
input double MinImbalanceRatio = 2.0;
input bool ShowVolumeNumbers = true;
input bool ShowDeltaLine = true;
input color TextColor = clrWhite;
input int FontSize = 8;

double BuyVolumeBuffer[];
double SellVolumeBuffer[];
double ImbalanceBuffer[];
double DeltaBuffer[];
double TempBuyBuffer[];
double TempSellBuffer[];
double TempImbalanceBuffer[];
double TempDeltaBuffer[];

struct VolumeData
{
    double price_levels[];
    long buy_volumes[];
    long sell_volumes[];
    long total_volumes[];
    bool imbalances[];
    long bar_buy_total;
    long bar_sell_total;
    double bar_delta;
};

VolumeData volume_data[];

int OnInit()
{
    SetIndexBuffer(0, BuyVolumeBuffer, INDICATOR_DATA);
    SetIndexBuffer(1, SellVolumeBuffer, INDICATOR_DATA);
    SetIndexBuffer(2, ImbalanceBuffer, INDICATOR_DATA);
    SetIndexBuffer(3, DeltaBuffer, INDICATOR_DATA);
    SetIndexBuffer(4, TempBuyBuffer, INDICATOR_CALCULATIONS);
    SetIndexBuffer(5, TempSellBuffer, INDICATOR_CALCULATIONS);
    SetIndexBuffer(6, TempImbalanceBuffer, INDICATOR_CALCULATIONS);
    SetIndexBuffer(7, TempDeltaBuffer, INDICATOR_CALCULATIONS);
    
    ArraySetAsSeries(BuyVolumeBuffer, true);
    ArraySetAsSeries(SellVolumeBuffer, true);
    ArraySetAsSeries(ImbalanceBuffer, true);
    ArraySetAsSeries(DeltaBuffer, true);
    
    ArrayResize(volume_data, BarsToAnalyze);
    
    for(int i = 0; i < BarsToAnalyze; i++)
    {
        ArrayResize(volume_data[i].price_levels, PriceLevelsPerBar);
        ArrayResize(volume_data[i].buy_volumes, PriceLevelsPerBar);
        ArrayResize(volume_data[i].sell_volumes, PriceLevelsPerBar);
        ArrayResize(volume_data[i].total_volumes, PriceLevelsPerBar);
        ArrayResize(volume_data[i].imbalances, PriceLevelsPerBar);
        volume_data[i].bar_buy_total = 0;
        volume_data[i].bar_sell_total = 0;
        volume_data[i].bar_delta = 0;
    }
    
    IndicatorSetString(INDICATOR_SHORTNAME, "OrderFlow Separate");
    IndicatorSetInteger(INDICATOR_DIGITS, 0);
    IndicatorSetDouble(INDICATOR_MINIMUM, 0);
    IndicatorSetDouble(INDICATOR_MAXIMUM, 10000);
    
    return INIT_SUCCEEDED;
}

int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime& time[],
                const double& open[],
                const double& high[],
                const double& low[],
                const double& close[],
                const long& tick_volume[],
                const long& volume[],
                const int& spread[])
{
    if(rates_total < BarsToAnalyze) return 0;
    
    int limit = rates_total - prev_calculated;
    if(limit > BarsToAnalyze) limit = BarsToAnalyze;
    if(limit <= 0 && prev_calculated > 0) limit = 1;
    
    for(int i = 0; i < limit && i < rates_total; i++)
    {
        AnalyzeBarVolume(i, rates_total, open, high, low, close, tick_volume, volume);
        
        BuyVolumeBuffer[i] = volume_data[i].bar_buy_total;
        SellVolumeBuffer[i] = -volume_data[i].bar_sell_total;
        DeltaBuffer[i] = volume_data[i].bar_delta;
        
        ImbalanceBuffer[i] = 0;
        for(int j = 0; j < PriceLevelsPerBar; j++)
        {
            if(volume_data[i].imbalances[j])
            {
                ImbalanceBuffer[i] = MathMax(volume_data[i].buy_volumes[j], volume_data[i].sell_volumes[j]);
                break;
            }
        }
    }
    
    DrawVolumeProfile(rates_total, time);
    
    return rates_total;
}

void AnalyzeBarVolume(int bar_index, int rates_total, const double& open[], const double& high[], const double& low[], const double& close[], const long& tick_volume[], const long& volume[])
{
    if(bar_index >= BarsToAnalyze || bar_index >= rates_total) return;
    
    int actual_index = rates_total - 1 - bar_index;
    if(actual_index < 0) return;
    
    double bar_open = open[actual_index];
    double bar_high = high[actual_index];
    double bar_low = low[actual_index];
    double bar_close = close[actual_index];
    long bar_volume = tick_volume[actual_index];
    
    if(bar_volume == 0) bar_volume = 100;
    
    double price_step = (bar_high - bar_low) / PriceLevelsPerBar;
    if(price_step <= 0) price_step = _Point;
    
    volume_data[bar_index].bar_buy_total = 0;
    volume_data[bar_index].bar_sell_total = 0;
    
    for(int level = 0; level < PriceLevelsPerBar; level++)
    {
        volume_data[bar_index].price_levels[level] = bar_low + (level * price_step);
        
        long level_volume = bar_volume / PriceLevelsPerBar;
        if(level_volume <= 0) level_volume = 1;
        
        double level_price = volume_data[bar_index].price_levels[level];
        double price_from_open = level_price - bar_open;
        double price_from_close = level_price - bar_close;
        
        if(bar_close > bar_open)
        {
            if(level_price >= bar_open && level_price <= bar_close)
            {
                double buy_ratio = 0.7 + (0.2 * (level_price - bar_open) / (bar_close - bar_open));
                volume_data[bar_index].buy_volumes[level] = (long)(level_volume * buy_ratio);
                volume_data[bar_index].sell_volumes[level] = level_volume - volume_data[bar_index].buy_volumes[level];
            }
            else if(level_price > bar_close)
            {
                volume_data[bar_index].buy_volumes[level] = (long)(level_volume * 0.8);
                volume_data[bar_index].sell_volumes[level] = level_volume - volume_data[bar_index].buy_volumes[level];
            }
            else
            {
                volume_data[bar_index].buy_volumes[level] = (long)(level_volume * 0.3);
                volume_data[bar_index].sell_volumes[level] = level_volume - volume_data[bar_index].buy_volumes[level];
            }
        }
        else if(bar_close < bar_open)
        {
            if(level_price <= bar_open && level_price >= bar_close)
            {
                double sell_ratio = 0.7 + (0.2 * (bar_open - level_price) / (bar_open - bar_close));
                volume_data[bar_index].sell_volumes[level] = (long)(level_volume * sell_ratio);
                volume_data[bar_index].buy_volumes[level] = level_volume - volume_data[bar_index].sell_volumes[level];
            }
            else if(level_price < bar_close)
            {
                volume_data[bar_index].sell_volumes[level] = (long)(level_volume * 0.8);
                volume_data[bar_index].buy_volumes[level] = level_volume - volume_data[bar_index].sell_volumes[level];
            }
            else
            {
                volume_data[bar_index].sell_volumes[level] = (long)(level_volume * 0.3);
                volume_data[bar_index].buy_volumes[level] = level_volume - volume_data[bar_index].sell_volumes[level];
            }
        }
        else
        {
            volume_data[bar_index].buy_volumes[level] = level_volume / 2;
            volume_data[bar_index].sell_volumes[level] = level_volume / 2;
        }
        
        if(volume_data[bar_index].buy_volumes[level] < 0) volume_data[bar_index].buy_volumes[level] = 0;
        if(volume_data[bar_index].sell_volumes[level] < 0) volume_data[bar_index].sell_volumes[level] = 0;
        
        volume_data[bar_index].total_volumes[level] = volume_data[bar_index].buy_volumes[level] + volume_data[bar_index].sell_volumes[level];
        
        volume_data[bar_index].bar_buy_total += volume_data[bar_index].buy_volumes[level];
        volume_data[bar_index].bar_sell_total += volume_data[bar_index].sell_volumes[level];
        
        double buy_sell_ratio = 0;
        if(volume_data[bar_index].sell_volumes[level] > 0)
            buy_sell_ratio = (double)volume_data[bar_index].buy_volumes[level] / volume_data[bar_index].sell_volumes[level];
        
        volume_data[bar_index].imbalances[level] = false;
        if(buy_sell_ratio >= MinImbalanceRatio || (volume_data[bar_index].sell_volumes[level] > 0 && buy_sell_ratio <= (1.0 / MinImbalanceRatio)))
        {
            volume_data[bar_index].imbalances[level] = true;
        }
        else if(volume_data[bar_index].buy_volumes[level] > 0 && volume_data[bar_index].sell_volumes[level] == 0)
        {
            volume_data[bar_index].imbalances[level] = true;
        }
        else if(volume_data[bar_index].sell_volumes[level] > 0 && volume_data[bar_index].buy_volumes[level] == 0)
        {
            volume_data[bar_index].imbalances[level] = true;
        }
    }
    
    volume_data[bar_index].bar_delta = volume_data[bar_index].bar_buy_total - volume_data[bar_index].bar_sell_total;
}

void DrawVolumeProfile(int rates_total, const datetime& time[])
{
    if(!ShowVolumeNumbers) return;
    
    ObjectsDeleteAll(0, "OrderFlow_");
    
    for(int bar = 0; bar < BarsToAnalyze && bar < rates_total; bar++)
    {
        int actual_index = rates_total - 1 - bar;
        if(actual_index < 0) continue;
        
        datetime bar_time = time[actual_index];
        
        for(int level = 0; level < PriceLevelsPerBar; level++)
        {
            if(volume_data[bar].total_volumes[level] > 0)
            {
                string obj_name = StringFormat("OrderFlow_Bar%d_Level%d", bar, level);
                double y_position = (double)bar + (double)level / PriceLevelsPerBar;
                
                ObjectCreate(0, obj_name, OBJ_TEXT, ChartWindowFind(), bar_time, y_position);
                
                string volume_text = StringFormat("B:%d|S:%d", 
                                                volume_data[bar].buy_volumes[level], 
                                                volume_data[bar].sell_volumes[level]);
                
                if(volume_data[bar].imbalances[level])
                {
                    volume_text = "[" + volume_text + "]";
                    ObjectSetInteger(0, obj_name, OBJPROP_COLOR, clrYellow);
                }
                else
                {
                    color text_color = (volume_data[bar].buy_volumes[level] > volume_data[bar].sell_volumes[level]) ? clrLimeGreen : clrRed;
                    ObjectSetInteger(0, obj_name, OBJPROP_COLOR, text_color);
                }
                
                ObjectSetString(0, obj_name, OBJPROP_TEXT, volume_text);
                ObjectSetInteger(0, obj_name, OBJPROP_FONTSIZE, FontSize);
                ObjectSetInteger(0, obj_name, OBJPROP_ANCHOR, ANCHOR_LEFT);
            }
        }
        
        string delta_obj = StringFormat("OrderFlow_Delta_%d", bar);
        ObjectCreate(0, delta_obj, OBJ_TEXT, ChartWindowFind(), bar_time, bar);
        ObjectSetString(0, delta_obj, OBJPROP_TEXT, StringFormat("Δ:%.0f", volume_data[bar].bar_delta));
        ObjectSetInteger(0, delta_obj, OBJPROP_COLOR, volume_data[bar].bar_delta > 0 ? clrLimeGreen : clrRed);
        ObjectSetInteger(0, delta_obj, OBJPROP_FONTSIZE, FontSize + 1);
        ObjectSetInteger(0, delta_obj, OBJPROP_ANCHOR, ANCHOR_CENTER);
    }
}

void OnDeinit(const int reason)
{
    ObjectsDeleteAll(0, "OrderFlow_");
    Comment("");
}