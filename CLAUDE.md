# Environment
- windows 10
- compiler path: C:\Program Files\FxPro - MetaTrader 5\MetaEditor64.exe
- compile log path: @.\CompileLog.log
- current terminal: C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\0148BD5691B65B0F2157627A4231F3DE

# Project description
- Programming on language MT5
- Indicator, script, and expert advisor development and sandbox testing

# Powershell commands
- Compile indicator:
```powershell
powershell -Command "& 'C:\Program Files\FxPro -
      MetaTrader 5\MetaEditor64.exe' '/compile:I:\workspace\tradi
      ng\sandbox\mt5\OrderFlowOriginal.mq5' '/log:I:\workspace\trading\sandbox\mt5\CompileLog.log'"
```
- Copy compiled file:
```powershell
powershell -Command "Copy-Item -Path 'I:\workspace\trading\sandbox\mt5\OrderFlowOriginal.ex5' -Destination 'C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\current_terminal_id\MQL5\Indicators\'"
```

# Workflow
- Make sure to run the compile command after making changes to the code
- Use the compile log to debug issues
- If need help. use context7 mcp or the MQL5 documentation

# Documentation
- Use the MQL5 documentation for reference: https://www.mql5.com/en/docs

# Testing
- Test indicators, scripts, and expert advisors in the MetaTrader 5 terminal
- Use the sandbox environment for testing before deploying to production    
