#property copyright "Trading Sandbox"
#property version   "1.00"
#property indicator_separate_window
#property indicator_buffers 0
#property indicator_plots 0

// Properties from orderflow_indicator_spec.md
input double TickSizeInPips = 20.0;
input int NumberOfBarsToProcess = 50;
input color BuyVolumeColor = clrBlue;
input color SellVolumeColor = clrRed;
input double VolumeAdjustmentMultiplier = 1.0;
input bool BigNumbersFormatting = true;
input bool ShowDelta = true;
input bool ShowData = true;
input bool ShowImbalances = true;
input int ImbalancesMultiplier = 3;
input color ChartBackgroundColor = clrWhite;
input int Spacing = 2;
input bool RemoveGrid = true;
input color ChartTextGridColor = clrBlack;

// Additional display settings
input int PriceLevelsPerBar = 15;
input int FontSize = 8;
input int ControlPanelX = 50;
input int ControlPanelY = 50;

struct PriceLevel
{
    double price;
    long buy_volume;
    long sell_volume;
    long total_volume;
    double delta;
    bool is_imbalance;
};

struct OrderFlowBar
{
    PriceLevel levels[];
    long total_buy;
    long total_sell;
    double cumulative_delta;
    datetime bar_time;
    double bar_open;
    double bar_high;
    double bar_low;
    double bar_close;
    long bar_volume;
    string time_label;
};

OrderFlowBar orderflow_bars[];
bool indicator_status = true;
double price_range_min = 0;
double price_range_max = 0;

int OnInit()
{
    // Initialize arrays
    ArrayResize(orderflow_bars, NumberOfBarsToProcess);
    
    for(int i = 0; i < NumberOfBarsToProcess; i++)
    {
        ArrayResize(orderflow_bars[i].levels, PriceLevelsPerBar);
        orderflow_bars[i].total_buy = 0;
        orderflow_bars[i].total_sell = 0;
        orderflow_bars[i].cumulative_delta = 0;
        orderflow_bars[i].time_label = "";
    }
    
    // Set chart properties
    if(RemoveGrid)
    {
        ChartSetInteger(0, CHART_SHOW_GRID, false);
    }
    
    ChartSetInteger(0, CHART_COLOR_BACKGROUND, ChartBackgroundColor);
    ChartSetInteger(0, CHART_COLOR_GRID, ChartTextGridColor);
    
    IndicatorSetString(INDICATOR_SHORTNAME, "OrderFlow Original");
    IndicatorSetInteger(INDICATOR_DIGITS, 2);
    
    CreateControlPanel();
    
    return INIT_SUCCEEDED;
}

int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime& time[],
                const double& open[],
                const double& high[],
                const double& low[],
                const double& close[],
                const long& tick_volume[],
                const long& volume[],
                const int& spread[])
{
    if(rates_total < NumberOfBarsToProcess || !indicator_status) return 0;
    
    CalculatePriceRange(rates_total, high, low);
    
    // Process only the most recent bars
    int bars_to_show = MathMin(10, NumberOfBarsToProcess); // Show max 10 bars like original
    
    for(int i = 0; i < bars_to_show && i < rates_total; i++)
    {
        int bar_index = rates_total - 1 - i;
        BuildOrderFlowBar(i, bar_index, time, open, high, low, close, tick_volume, volume);
    }
    
    if(ShowData)
        DrawOrderFlowBars(bars_to_show, time, rates_total);
    
    return rates_total;
}

void CalculatePriceRange(int rates_total, const double& high[], const double& low[])
{
    price_range_min = DBL_MAX;
    price_range_max = -DBL_MAX;
    
    int bars_to_check = MathMin(10, NumberOfBarsToProcess);
    
    for(int i = 0; i < bars_to_check && i < rates_total; i++)
    {
        int bar_index = rates_total - 1 - i;
        if(high[bar_index] > price_range_max) price_range_max = high[bar_index];
        if(low[bar_index] < price_range_min) price_range_min = low[bar_index];
    }
    
    double range_extension = (price_range_max - price_range_min) * 0.05;
    price_range_min -= range_extension;
    price_range_max += range_extension;
}

void BuildOrderFlowBar(int bar_index, int rates_index, const datetime& time[], const double& open[], const double& high[], const double& low[], const double& close[], const long& tick_volume[], const long& volume[])
{
    orderflow_bars[bar_index].bar_time = time[rates_index];
    orderflow_bars[bar_index].bar_open = open[rates_index];
    orderflow_bars[bar_index].bar_high = high[rates_index];
    orderflow_bars[bar_index].bar_low = low[rates_index];
    orderflow_bars[bar_index].bar_close = close[rates_index];
    orderflow_bars[bar_index].bar_volume = tick_volume[rates_index];
    
    // Create time label like "17:30"
    MqlDateTime dt;
    TimeToStruct(time[rates_index], dt);
    orderflow_bars[bar_index].time_label = StringFormat("%02d:%02d", dt.hour, dt.min);
    
    if(orderflow_bars[bar_index].bar_volume == 0) orderflow_bars[bar_index].bar_volume = 100;
    
    double price_step = (price_range_max - price_range_min) / PriceLevelsPerBar;
    if(price_step <= 0) price_step = _Point;
    
    orderflow_bars[bar_index].total_buy = 0;
    orderflow_bars[bar_index].total_sell = 0;
    
    long base_volume_per_level = orderflow_bars[bar_index].bar_volume / PriceLevelsPerBar;
    if(base_volume_per_level <= 0) base_volume_per_level = 1;
    
    for(int level = 0; level < PriceLevelsPerBar; level++)
    {
        orderflow_bars[bar_index].levels[level].price = price_range_min + (level * price_step);
        
        double level_price = orderflow_bars[bar_index].levels[level].price;
        long level_total_volume = base_volume_per_level;
        
        // Enhanced volume distribution based on price action
        if(level_price >= orderflow_bars[bar_index].bar_low && level_price <= orderflow_bars[bar_index].bar_high)
        {
            double bar_range = orderflow_bars[bar_index].bar_high - orderflow_bars[bar_index].bar_low;
            if(bar_range > 0)
            {
                double distance_from_middle = MathAbs(level_price - (orderflow_bars[bar_index].bar_high + orderflow_bars[bar_index].bar_low) / 2.0);
                double distance_factor = 1.8 - (distance_from_middle / bar_range);
                if(distance_factor < 0.4) distance_factor = 0.4;
                if(distance_factor > 2.2) distance_factor = 2.2;
                
                level_total_volume = (long)(base_volume_per_level * distance_factor * VolumeAdjustmentMultiplier);
            }
        }
        else
        {
            level_total_volume = (long)(base_volume_per_level * 0.15);
        }
        
        // Calculate buy/sell split based on bar direction
        double buy_ratio = 0.5;
        
        if(orderflow_bars[bar_index].bar_close > orderflow_bars[bar_index].bar_open)
        {
            if(level_price >= orderflow_bars[bar_index].bar_open && level_price <= orderflow_bars[bar_index].bar_close)
            {
                double progress = (level_price - orderflow_bars[bar_index].bar_open) / (orderflow_bars[bar_index].bar_close - orderflow_bars[bar_index].bar_open);
                buy_ratio = 0.65 + (progress * 0.25);
            }
            else if(level_price > orderflow_bars[bar_index].bar_close)
            {
                buy_ratio = 0.8;
            }
            else
            {
                buy_ratio = 0.3;
            }
        }
        else if(orderflow_bars[bar_index].bar_close < orderflow_bars[bar_index].bar_open)
        {
            if(level_price <= orderflow_bars[bar_index].bar_open && level_price >= orderflow_bars[bar_index].bar_close)
            {
                double progress = (orderflow_bars[bar_index].bar_open - level_price) / (orderflow_bars[bar_index].bar_open - orderflow_bars[bar_index].bar_close);
                buy_ratio = 0.35 - (progress * 0.25);
            }
            else if(level_price < orderflow_bars[bar_index].bar_close)
            {
                buy_ratio = 0.2;
            }
            else
            {
                buy_ratio = 0.7;
            }
        }
        
        orderflow_bars[bar_index].levels[level].buy_volume = (long)(level_total_volume * buy_ratio);
        orderflow_bars[bar_index].levels[level].sell_volume = level_total_volume - orderflow_bars[bar_index].levels[level].buy_volume;
        orderflow_bars[bar_index].levels[level].total_volume = level_total_volume;
        orderflow_bars[bar_index].levels[level].delta = orderflow_bars[bar_index].levels[level].buy_volume - orderflow_bars[bar_index].levels[level].sell_volume;
        
        // Detect imbalances
        orderflow_bars[bar_index].levels[level].is_imbalance = false;
        if(ShowImbalances && orderflow_bars[bar_index].levels[level].sell_volume > 0)
        {
            double ratio = (double)orderflow_bars[bar_index].levels[level].buy_volume / orderflow_bars[bar_index].levels[level].sell_volume;
            if(ratio >= ImbalancesMultiplier || ratio <= (1.0 / ImbalancesMultiplier))
            {
                orderflow_bars[bar_index].levels[level].is_imbalance = true;
            }
        }
        
        orderflow_bars[bar_index].total_buy += orderflow_bars[bar_index].levels[level].buy_volume;
        orderflow_bars[bar_index].total_sell += orderflow_bars[bar_index].levels[level].sell_volume;
    }
    
    orderflow_bars[bar_index].cumulative_delta = orderflow_bars[bar_index].total_buy - orderflow_bars[bar_index].total_sell;
}

void DrawOrderFlowBars(int bars_to_show, const datetime& time[], int rates_total)
{
    ObjectsDeleteAll(0, "OF_");
    
    int window_index = ChartWindowFind();
    if(window_index < 0) return;
    
    // Calculate positions for vertical bars
    int start_x = 150; // Start position for bars
    int bar_width = 80; // Width of each bar
    int bar_spacing = 100; // Space between bars
    
    for(int bar = 0; bar < bars_to_show; bar++)
    {
        if(orderflow_bars[bar].bar_time == 0) continue;
        
        int bar_x = start_x + (bar * bar_spacing);
        int bar_top = 100;
        int level_height = 18;
        
        // Create background rectangle for the bar
        string bg_name = StringFormat("OF_BG_%d", bar);
        CreateRectangle(bg_name, bar_x - 35, bar_top - 10, bar_x + 35, bar_top + (PriceLevelsPerBar * level_height) + 50, clrLightGray, true);
        
        // Draw price levels with buy/sell volumes
        for(int level = PriceLevelsPerBar - 1; level >= 0; level--) // Draw from top to bottom
        {
            int level_y = bar_top + ((PriceLevelsPerBar - 1 - level) * level_height);
            
            long buy_vol = orderflow_bars[bar].levels[level].buy_volume;
            long sell_vol = orderflow_bars[bar].levels[level].sell_volume;
            
            if(buy_vol > 0 || sell_vol > 0)
            {
                // Buy volume (left side, blue)
                string buy_text_name = StringFormat("OF_BUY_%d_%d", bar, level);
                CreateText(buy_text_name, bar_x - 25, level_y, FormatVolume(buy_vol), BuyVolumeColor, FontSize);
                
                // Sell volume (right side, red)
                string sell_text_name = StringFormat("OF_SELL_%d_%d", bar, level);
                CreateText(sell_text_name, bar_x + 15, level_y, FormatVolume(sell_vol), SellVolumeColor, FontSize);
                
                // Highlight imbalances
                if(orderflow_bars[bar].levels[level].is_imbalance)
                {
                    string imb_rect = StringFormat("OF_IMB_%d_%d", bar, level);
                    CreateRectangle(imb_rect, bar_x - 30, level_y - 2, bar_x + 30, level_y + 12, clrYellow, false);
                }
            }
        }
        
        // Time label
        string time_label_name = StringFormat("OF_TIME_%d", bar);
        CreateText(time_label_name, bar_x, bar_top + (PriceLevelsPerBar * level_height) + 15, orderflow_bars[bar].time_label, ChartTextGridColor, FontSize + 1);
        
        // Delta value
        if(ShowDelta)
        {
            string delta_name = StringFormat("OF_DELTA_%d", bar);
            color delta_color = orderflow_bars[bar].cumulative_delta >= 0 ? BuyVolumeColor : SellVolumeColor;
            string delta_text = StringFormat("%s%.0f", orderflow_bars[bar].cumulative_delta >= 0 ? "" : "", orderflow_bars[bar].cumulative_delta);
            CreateText(delta_name, bar_x, bar_top + (PriceLevelsPerBar * level_height) + 35, delta_text, delta_color, FontSize + 1);
        }
    }
}

void CreateControlPanel()
{
    // Main panel background
    CreateRectangle("OF_PANEL_BG", ControlPanelX, ControlPanelY, ControlPanelX + 200, ControlPanelY + 120, clrLightGray, true);
    
    // Title
    CreateText("OF_PANEL_TITLE", ControlPanelX + 10, ControlPanelY + 10, "OrderFlow Footprint", clrBlack, FontSize + 2);
    
    // Status
    string status_text = StringFormat("Status: %s", indicator_status ? "ON" : "OFF");
    color status_color = indicator_status ? clrGreen : clrRed;
    CreateText("OF_PANEL_STATUS", ControlPanelX + 10, ControlPanelY + 30, status_text, status_color, FontSize);
    
    // Initialized button
    CreateButton("OF_INITIALIZED", ControlPanelX + 20, ControlPanelY + 50, 160, 25, "Initialized", clrBlue, clrWhite);
    
    // Refresh button
    CreateButton("OF_REFRESH", ControlPanelX + 20, ControlPanelY + 85, 160, 25, "Refresh", clrGreen, clrWhite);
}

void CreateRectangle(string name, int x1, int y1, int x2, int y2, color clr, bool fill)
{
    ObjectCreate(0, name, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x1);
    ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y1);
    ObjectSetInteger(0, name, OBJPROP_XSIZE, x2 - x1);
    ObjectSetInteger(0, name, OBJPROP_YSIZE, y2 - y1);
    ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
    ObjectSetInteger(0, name, OBJPROP_BGCOLOR, clr);
    ObjectSetInteger(0, name, OBJPROP_FILL, fill);
    ObjectSetInteger(0, name, OBJPROP_BACK, false);
}

void CreateText(string name, int x, int y, string text, color clr, int size)
{
    ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
    ObjectSetString(0, name, OBJPROP_TEXT, text);
    ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
    ObjectSetInteger(0, name, OBJPROP_FONTSIZE, size);
    ObjectSetString(0, name, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
}

void CreateButton(string name, int x, int y, int width, int height, string text, color bg_color, color text_color)
{
    ObjectCreate(0, name, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
    ObjectSetInteger(0, name, OBJPROP_XSIZE, width);
    ObjectSetInteger(0, name, OBJPROP_YSIZE, height);
    ObjectSetInteger(0, name, OBJPROP_BGCOLOR, bg_color);
    ObjectSetInteger(0, name, OBJPROP_BORDER_COLOR, clrBlack);
    ObjectSetInteger(0, name, OBJPROP_FILL, true);
    
    string text_name = name + "_TEXT";
    ObjectCreate(0, text_name, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, text_name, OBJPROP_XDISTANCE, x + width/2);
    ObjectSetInteger(0, text_name, OBJPROP_YDISTANCE, y + height/2 - 5);
    ObjectSetString(0, text_name, OBJPROP_TEXT, text);
    ObjectSetInteger(0, text_name, OBJPROP_COLOR, text_color);
    ObjectSetInteger(0, text_name, OBJPROP_FONTSIZE, FontSize);
    ObjectSetInteger(0, text_name, OBJPROP_ANCHOR, ANCHOR_CENTER);
}

string FormatVolume(long volume)
{
    if(!BigNumbersFormatting) return IntegerToString(volume);
    
    if(volume >= 1000000)
        return StringFormat("%.1fM", (double)volume / 1000000.0);
    else if(volume >= 1000)
        return StringFormat("%.1fK", (double)volume / 1000.0);
    else
        return IntegerToString(volume);
}

void OnChartEvent(const int id, const long& lparam, const double& dparam, const string& sparam)
{
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        if(sparam == "OF_INITIALIZED")
        {
            indicator_status = true;
            ObjectSetInteger(0, "OF_INITIALIZED", OBJPROP_BGCOLOR, clrDarkBlue);
            Sleep(200);
            ObjectSetInteger(0, "OF_INITIALIZED", OBJPROP_BGCOLOR, clrBlue);
            ChartRedraw();
        }
        else if(sparam == "OF_REFRESH")
        {
            ObjectSetInteger(0, "OF_REFRESH", OBJPROP_BGCOLOR, clrDarkGreen);
            ObjectsDeleteAll(0, "OF_");
            CreateControlPanel();
            Sleep(200);
            ObjectSetInteger(0, "OF_REFRESH", OBJPROP_BGCOLOR, clrGreen);
            ChartRedraw();
        }
    }
}

void OnDeinit(const int reason)
{
    ObjectsDeleteAll(0, "OF_");
    Comment("");
    
    // Restore chart settings
    ChartSetInteger(0, CHART_SHOW_GRID, true);
}