#property copyright "Trading Sandbox"
#property version   "1.00"
#property indicator_separate_window
#property indicator_buffers 0
#property indicator_plots 0

input int MaxBarsVisible = 10;
input int PriceLevelsPerBar = 20;
input double MinImbalanceRatio = 3.0;
input color BuyVolumeColor = clrLimeGreen;
input color SellVolumeColor = clrRed;
input color ImbalanceColor = clrYellow;
input color BackgroundColor = clrBlack;
input int FontSize = 8;
input bool ShowVolumeNumbers = true;
input bool ShowDeltaPanel = true;

struct PriceLevel
{
    double price;
    long buy_volume;
    long sell_volume;
    long total_volume;
    double delta;
    bool is_imbalance;
    double volume_ratio;
};

struct BarOrderFlow
{
    PriceLevel levels[];
    long total_buy;
    long total_sell;
    double cumulative_delta;
    datetime bar_time;
    double bar_open;
    double bar_high;
    double bar_low;
    double bar_close;
    long bar_volume;
    long max_level_volume;
};

BarOrderFlow orderflow_bars[];
double price_range_min = 0;
double price_range_max = 0;
long global_max_volume = 1;

int OnInit()
{
    ArrayResize(orderflow_bars, MaxBarsVisible);
    
    for(int i = 0; i < MaxBarsVisible; i++)
    {
        ArrayResize(orderflow_bars[i].levels, PriceLevelsPerBar);
        orderflow_bars[i].total_buy = 0;
        orderflow_bars[i].total_sell = 0;
        orderflow_bars[i].cumulative_delta = 0;
        orderflow_bars[i].max_level_volume = 1;
    }
    
    IndicatorSetString(INDICATOR_SHORTNAME, "OrderFlow Focused");
    IndicatorSetInteger(INDICATOR_DIGITS, 5);
    
    return INIT_SUCCEEDED;
}

int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime& time[],
                const double& open[],
                const double& high[],
                const double& low[],
                const double& close[],
                const long& tick_volume[],
                const long& volume[],
                const int& spread[])
{
    if(rates_total < MaxBarsVisible) return 0;
    
    CalculatePriceRange(rates_total, high, low);
    
    for(int i = 0; i < MaxBarsVisible && i < rates_total; i++)
    {
        int bar_index = rates_total - 1 - i;
        BuildBarOrderFlow(i, bar_index, time, open, high, low, close, tick_volume, volume);
    }
    
    FindGlobalMaxVolume();
    DrawOrderFlowProfile();
    
    if(ShowDeltaPanel)
        DrawDeltaPanel();
    
    return rates_total;
}

void CalculatePriceRange(int rates_total, const double& high[], const double& low[])
{
    price_range_min = DBL_MAX;
    price_range_max = -DBL_MAX;
    
    for(int i = 0; i < MaxBarsVisible && i < rates_total; i++)
    {
        int bar_index = rates_total - 1 - i;
        if(high[bar_index] > price_range_max) price_range_max = high[bar_index];
        if(low[bar_index] < price_range_min) price_range_min = low[bar_index];
    }
    
    double range_extension = (price_range_max - price_range_min) * 0.1;
    price_range_min -= range_extension;
    price_range_max += range_extension;
}

void BuildBarOrderFlow(int bar_index, int rates_index, const datetime& time[], const double& open[], const double& high[], const double& low[], const double& close[], const long& tick_volume[], const long& volume[])
{
    orderflow_bars[bar_index].bar_time = time[rates_index];
    orderflow_bars[bar_index].bar_open = open[rates_index];
    orderflow_bars[bar_index].bar_high = high[rates_index];
    orderflow_bars[bar_index].bar_low = low[rates_index];
    orderflow_bars[bar_index].bar_close = close[rates_index];
    orderflow_bars[bar_index].bar_volume = tick_volume[rates_index];
    
    if(orderflow_bars[bar_index].bar_volume == 0) orderflow_bars[bar_index].bar_volume = 100;
    
    double price_step = (price_range_max - price_range_min) / PriceLevelsPerBar;
    if(price_step <= 0) price_step = _Point;
    
    orderflow_bars[bar_index].total_buy = 0;
    orderflow_bars[bar_index].total_sell = 0;
    orderflow_bars[bar_index].max_level_volume = 1;
    
    long base_volume_per_level = orderflow_bars[bar_index].bar_volume / PriceLevelsPerBar;
    if(base_volume_per_level <= 0) base_volume_per_level = 1;
    
    for(int level = 0; level < PriceLevelsPerBar; level++)
    {
        orderflow_bars[bar_index].levels[level].price = price_range_min + (level * price_step);
        
        double level_price = orderflow_bars[bar_index].levels[level].price;
        double bar_range = orderflow_bars[bar_index].bar_high - orderflow_bars[bar_index].bar_low;
        
        long level_total_volume = base_volume_per_level;
        
        if(level_price >= orderflow_bars[bar_index].bar_low && level_price <= orderflow_bars[bar_index].bar_high)
        {
            double distance_factor = 1.0;
            if(bar_range > 0)
            {
                double distance_from_middle = MathAbs(level_price - (orderflow_bars[bar_index].bar_high + orderflow_bars[bar_index].bar_low) / 2.0);
                distance_factor = 1.5 - (distance_from_middle / bar_range);
                if(distance_factor < 0.3) distance_factor = 0.3;
                if(distance_factor > 2.0) distance_factor = 2.0;
            }
            
            level_total_volume = (long)(base_volume_per_level * distance_factor);
        }
        else
        {
            level_total_volume = (long)(base_volume_per_level * 0.1);
        }
        
        double buy_ratio = 0.5;
        
        if(orderflow_bars[bar_index].bar_close > orderflow_bars[bar_index].bar_open)
        {
            if(level_price >= orderflow_bars[bar_index].bar_open && level_price <= orderflow_bars[bar_index].bar_close)
            {
                double progress = (level_price - orderflow_bars[bar_index].bar_open) / (orderflow_bars[bar_index].bar_close - orderflow_bars[bar_index].bar_open);
                buy_ratio = 0.6 + (progress * 0.3);
            }
            else if(level_price > orderflow_bars[bar_index].bar_close)
            {
                buy_ratio = 0.75;
            }
            else
            {
                buy_ratio = 0.25;
            }
        }
        else if(orderflow_bars[bar_index].bar_close < orderflow_bars[bar_index].bar_open)
        {
            if(level_price <= orderflow_bars[bar_index].bar_open && level_price >= orderflow_bars[bar_index].bar_close)
            {
                double progress = (orderflow_bars[bar_index].bar_open - level_price) / (orderflow_bars[bar_index].bar_open - orderflow_bars[bar_index].bar_close);
                buy_ratio = 0.4 - (progress * 0.3);
            }
            else if(level_price < orderflow_bars[bar_index].bar_close)
            {
                buy_ratio = 0.25;
            }
            else
            {
                buy_ratio = 0.75;
            }
        }
        
        orderflow_bars[bar_index].levels[level].buy_volume = (long)(level_total_volume * buy_ratio);
        orderflow_bars[bar_index].levels[level].sell_volume = level_total_volume - orderflow_bars[bar_index].levels[level].buy_volume;
        orderflow_bars[bar_index].levels[level].total_volume = level_total_volume;
        orderflow_bars[bar_index].levels[level].delta = orderflow_bars[bar_index].levels[level].buy_volume - orderflow_bars[bar_index].levels[level].sell_volume;
        
        if(orderflow_bars[bar_index].levels[level].sell_volume > 0)
            orderflow_bars[bar_index].levels[level].volume_ratio = (double)orderflow_bars[bar_index].levels[level].buy_volume / orderflow_bars[bar_index].levels[level].sell_volume;
        else
            orderflow_bars[bar_index].levels[level].volume_ratio = 999.0;
        
        orderflow_bars[bar_index].levels[level].is_imbalance = false;
        if(orderflow_bars[bar_index].levels[level].volume_ratio >= MinImbalanceRatio || 
           orderflow_bars[bar_index].levels[level].volume_ratio <= (1.0 / MinImbalanceRatio))
        {
            orderflow_bars[bar_index].levels[level].is_imbalance = true;
        }
        
        orderflow_bars[bar_index].total_buy += orderflow_bars[bar_index].levels[level].buy_volume;
        orderflow_bars[bar_index].total_sell += orderflow_bars[bar_index].levels[level].sell_volume;
        
        if(orderflow_bars[bar_index].levels[level].total_volume > orderflow_bars[bar_index].max_level_volume)
            orderflow_bars[bar_index].max_level_volume = orderflow_bars[bar_index].levels[level].total_volume;
    }
    
    orderflow_bars[bar_index].cumulative_delta = orderflow_bars[bar_index].total_buy - orderflow_bars[bar_index].total_sell;
}

void FindGlobalMaxVolume()
{
    global_max_volume = 1;
    for(int bar = 0; bar < MaxBarsVisible; bar++)
    {
        if(orderflow_bars[bar].max_level_volume > global_max_volume)
            global_max_volume = orderflow_bars[bar].max_level_volume;
    }
}

void DrawOrderFlowProfile()
{
    ObjectsDeleteAll(0, "OrderFlow_");
    
    int window_index = ChartWindowFind();
    if(window_index < 0) return;
    
    for(int bar = 0; bar < MaxBarsVisible; bar++)
    {
        datetime bar_time = orderflow_bars[bar].bar_time;
        if(bar_time == 0) continue;
        
        for(int level = 0; level < PriceLevelsPerBar; level++)
        {
            double price = orderflow_bars[bar].levels[level].price;
            long buy_vol = orderflow_bars[bar].levels[level].buy_volume;
            long sell_vol = orderflow_bars[bar].levels[level].sell_volume;
            long total_vol = orderflow_bars[bar].levels[level].total_volume;
            
            if(total_vol <= 0) continue;
            
            double bar_width_seconds = PeriodSeconds() * 0.8;
            double volume_bar_width = ((double)total_vol / global_max_volume) * bar_width_seconds;
            if(volume_bar_width < bar_width_seconds * 0.1) volume_bar_width = bar_width_seconds * 0.1;
            
            string buy_rect_name = StringFormat("OrderFlow_Buy_%d_%d", bar, level);
            string sell_rect_name = StringFormat("OrderFlow_Sell_%d_%d", bar, level);
            
            double buy_width = (buy_vol > 0) ? (volume_bar_width * buy_vol / total_vol) : 0;
            double sell_width = (sell_vol > 0) ? (volume_bar_width * sell_vol / total_vol) : 0;
            
            if(buy_width > 0)
            {
                datetime buy_end_time = bar_time + (int)buy_width;
                ObjectCreate(0, buy_rect_name, OBJ_RECTANGLE, 0, bar_time, price - (_Point * 2), buy_end_time, price + (_Point * 2));
                ObjectSetInteger(0, buy_rect_name, OBJPROP_COLOR, orderflow_bars[bar].levels[level].is_imbalance ? ImbalanceColor : BuyVolumeColor);
                ObjectSetInteger(0, buy_rect_name, OBJPROP_FILL, true);
                ObjectSetInteger(0, buy_rect_name, OBJPROP_BACK, false);
            }
            
            if(sell_width > 0)
            {
                datetime sell_start_time = bar_time + (int)buy_width;
                datetime sell_end_time = sell_start_time + (int)sell_width;
                ObjectCreate(0, sell_rect_name, OBJ_RECTANGLE, 0, sell_start_time, price - (_Point * 2), sell_end_time, price + (_Point * 2));
                ObjectSetInteger(0, sell_rect_name, OBJPROP_COLOR, orderflow_bars[bar].levels[level].is_imbalance ? ImbalanceColor : SellVolumeColor);
                ObjectSetInteger(0, sell_rect_name, OBJPROP_FILL, true);
                ObjectSetInteger(0, sell_rect_name, OBJPROP_BACK, false);
            }
            
            if(ShowVolumeNumbers && total_vol > (global_max_volume * 0.05))
            {
                string text_name = StringFormat("OrderFlow_Text_%d_%d", bar, level);
                datetime text_time = bar_time + (int)(volume_bar_width * 1.1);
                
                ObjectCreate(0, text_name, OBJ_TEXT, 0, text_time, price);
                ObjectSetString(0, text_name, OBJPROP_TEXT, StringFormat("%d|%d", buy_vol, sell_vol));
                ObjectSetInteger(0, text_name, OBJPROP_COLOR, orderflow_bars[bar].levels[level].is_imbalance ? ImbalanceColor : clrWhite);
                ObjectSetInteger(0, text_name, OBJPROP_FONTSIZE, FontSize);
                ObjectSetInteger(0, text_name, OBJPROP_ANCHOR, ANCHOR_LEFT);
            }
        }
        
        string delta_name = StringFormat("OrderFlow_Delta_%d", bar);
        double mid_price = (price_range_min + price_range_max) / 2.0;
        ObjectCreate(0, delta_name, OBJ_TEXT, 0, bar_time, mid_price);
        ObjectSetString(0, delta_name, OBJPROP_TEXT, StringFormat("Δ:%.0f", orderflow_bars[bar].cumulative_delta));
        ObjectSetInteger(0, delta_name, OBJPROP_COLOR, orderflow_bars[bar].cumulative_delta > 0 ? BuyVolumeColor : SellVolumeColor);
        ObjectSetInteger(0, delta_name, OBJPROP_FONTSIZE, FontSize + 2);
        ObjectSetInteger(0, delta_name, OBJPROP_ANCHOR, ANCHOR_CENTER);
    }
}

void DrawDeltaPanel()
{
    long total_buy = 0, total_sell = 0;
    double total_delta = 0;
    
    for(int bar = 0; bar < MaxBarsVisible; bar++)
    {
        total_buy += orderflow_bars[bar].total_buy;
        total_sell += orderflow_bars[bar].total_sell;
        total_delta += orderflow_bars[bar].cumulative_delta;
    }
    
    string delta_panel = StringFormat("Bars:%d | Buy:%d | Sell:%d | Total Δ:%.0f | Max Vol:%d", 
                                     MaxBarsVisible, total_buy, total_sell, total_delta, global_max_volume);
    
    Comment(delta_panel);
}

void OnDeinit(const int reason)
{
    ObjectsDeleteAll(0, "OrderFlow_");
    Comment("");
}