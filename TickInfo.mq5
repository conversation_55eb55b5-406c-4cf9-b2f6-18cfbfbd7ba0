#property copyright "Trading Sandbox"
#property version   "1.00"
#property indicator_chart_window

input int FontSize = 12;
input color TextColor = clrWhite;
input int XPosition = 10;
input int YPosition = 30;

string tick_info_text = "";

int OnInit()
{
    return INIT_SUCCEEDED;
}

int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime& time[],
                const double& open[],
                const double& high[],
                const double& low[],
                const double& close[],
                const long& tick_volume[],
                const long& volume[],
                const int& spread[])
{
    MqlTick last_tick;
    if(SymbolInfoTick(_Symbol, last_tick))
    {
        tick_info_text = StringFormat("Symbol: %s\nTime: %s\nBid: %.5f\nAsk: %.5f\nLast: %.5f\nVolume: %d\nTime (ms): %d\nFlags: %d\nVolume Real: %.2f\nSpread: %.1f pips",
                                      _Symbol,
                                      TimeToString(last_tick.time, TIME_DATE|TIME_SECONDS),
                                      last_tick.bid,
                                      last_tick.ask,
                                      last_tick.last,
                                      last_tick.volume,
                                      last_tick.time_msc,
                                      last_tick.flags,
                                      last_tick.volume_real,
                                      (last_tick.ask - last_tick.bid) / _Point);
        
        Comment(tick_info_text);
    }
    
    return rates_total;
}

void OnDeinit(const int reason)
{
    Comment("");
}

void OnChartEvent(const int id,
                  const long &lparam,
                  const double &dparam,
                  const string &sparam)
{
    if(id == CHARTEVENT_CHART_CHANGE)
    {
        ChartRedraw();
    }
}